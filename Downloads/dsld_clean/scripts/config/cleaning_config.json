{"_comments": {"usage": "To run the DSLD cleaning script with this config:", "from_root_directory": "python scripts/clean_dsld_data.py --config scripts/config/cleaning_config.json", "from_scripts_directory": "python clean_dsld_data.py --config config/cleaning_config.json", "alternatives": ["python clean_dsld_data.py (uses this config by default when in scripts/ dir)", "python clean_dsld_data.py --resume (resume interrupted processing)", "python clean_dsld_data.py --dry-run (test without writing files)"], "workflow": "1. CLEANING (this phase) → 2. ENRICHMENT → 3. SCORING → 4. DEPLOYMENT"}, "paths": {"input_directory": "/Users/<USER>/Downloads/Gummie<PERSON>-<PERSON><PERSON><PERSON>-4294labels-8-4-25", "output_directory": "output_Gummies-<PERSON><PERSON><PERSON>", "reference_data": "data", "log_directory": "logs"}, "processing": {"_comment": "Optimized for performance: smaller batches with parallel processing", "batch_size": 500, "max_workers": 4, "chunk_size": 50}, "logging": {"level": "INFO", "log_to_console": true, "log_to_file": true, "file": "logs/dsld_cleaning.log", "max_bytes": 10485760, "backup_count": 5}, "validation": {"_comment": "Field validation and completeness scoring", "required_fields": {"critical": ["id", "fullName", "brandName", "ingredientRows"], "important": ["upcSku", "productType", "physicalState"], "optional": ["servingsPerContainer", "thumbnail", "netContents", "targetGroups", "images", "contacts", "events", "statements", "claims", "servingSizes"]}, "completeness_threshold": 75.0, "mapping_threshold": 75.0}, "normalization": {"_comment": "Enhanced normalizer settings with comprehensive ingredient mapping", "fuzzy_match_threshold": 85, "preserve_original_names": true, "extract_dosages": true, "detect_allergens": true, "flag_harmful_additives": true}, "options": {"_comment": "Processing options - set resume_processing=true to continue interrupted runs", "resume_processing": false, "dry_run": false, "generate_reports": true, "create_backups": true}}